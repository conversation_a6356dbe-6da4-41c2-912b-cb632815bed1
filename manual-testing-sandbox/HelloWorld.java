import java.net.InetAddress;
import java.net.UnknownHostException;

public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        System.out.println("Current time: " + getCurrentTime());
        System.out.println("Local IP address: " + getLocalIpAddress());
    }

    public static String getCurrentTime() {
        java.time.LocalTime currentTime = java.time.LocalTime.now();
        System.out.println(1);
        return currentTime.toString();
    }

    public static String getLocalIpAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            return "本地 IP 地址: " + localHost.getHostAddress();
        } catch (UnknownHostException e) {
            return "无法确定本地 IP 地址";
        }
    }
}
